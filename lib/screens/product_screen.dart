import 'package:flutter/material.dart';

class ProductScreen extends StatefulWidget {
  const ProductScreen({super.key});

  @override
  State<ProductScreen> createState() => _ProductScreenState();
}

class _ProductScreenState extends State<ProductScreen> {
  String _selectedPlatform = '全部平台';
  String _selectedCategory = '全部分类';
  bool _showSearch = false;
  
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _platformCodeController = TextEditingController();
  final TextEditingController _productCodeController = TextEditingController();
  final TextEditingController _brandController = TextEditingController();
  final TextEditingController _modelController = TextEditingController();
  final TextEditingController _minPriceController = TextEditingController();
  final TextEditingController _maxPriceController = TextEditingController();

  final List<String> _platforms = ['全部平台', '京东', '天猫', '淘宝', '拼多多', '苏宁'];
  final List<String> _categories = ['全部分类', '电子产品', '办公用品', '家居用品', '服装鞋帽', '食品饮料'];
  final List<String> _priceStatus = ['全部', '有报价', '无报价'];
  final List<String> _brandTypes = ['全部', '自主品牌', '代理品牌'];
  final List<String> _displayStatus = ['全部', '前台展示', '不展示'];

  String _selectedPriceStatus = '全部';
  String _selectedBrandType = '全部';
  String _selectedDisplayStatus = '全部';

  final List<ProductItem> _products = [
    ProductItem(
      name: 'iPhone 15 Pro',
      platformCode: 'JD001',
      productCode: 'IP15P001',
      brand: 'Apple',
      model: 'A3108',
      price: 8999.0,
      platform: '京东',
      category: '电子产品',
      isOwnBrand: false,
      isDisplayed: true,
      updateTime: DateTime.now().subtract(const Duration(hours: 2)),
    ),
    ProductItem(
      name: '联想ThinkPad X1',
      platformCode: 'TM002',
      productCode: 'TP001',
      brand: '联想',
      model: 'X1-2024',
      price: 12999.0,
      platform: '天猫',
      category: '电子产品',
      isOwnBrand: true,
      isDisplayed: true,
      updateTime: DateTime.now().subtract(const Duration(hours: 5)),
    ),
    ProductItem(
      name: '办公椅人体工学',
      platformCode: 'SN003',
      productCode: 'OC001',
      brand: '自主品牌',
      model: 'OC-2024',
      price: 899.0,
      platform: '苏宁',
      category: '办公用品',
      isOwnBrand: true,
      isDisplayed: false,
      updateTime: DateTime.now().subtract(const Duration(days: 1)),
    ),
  ];

  List<ProductItem> _filteredProducts = [];

  @override
  void initState() {
    super.initState();
    _filteredProducts = _products;
  }

  @override
  void dispose() {
    _searchController.dispose();
    _platformCodeController.dispose();
    _productCodeController.dispose();
    _brandController.dispose();
    _modelController.dispose();
    _minPriceController.dispose();
    _maxPriceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          '商品管理',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF6C63FF),
        elevation: 0,
        centerTitle: true,
        actions: [
          IconButton(
            icon: Icon(
              _showSearch ? Icons.search_off : Icons.search,
              color: Colors.white,
            ),
            onPressed: () {
              setState(() {
                _showSearch = !_showSearch;
              });
            },
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF6C63FF),
              Color(0xFFF7FAFC),
            ],
            stops: [0.0, 0.1],
          ),
        ),
        child: Column(
          children: [
            // 筛选区域
            Container(
              margin: const EdgeInsets.all(16),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: Column(
                children: [
                  // 平台和分类选择
                  Row(
                    children: [
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          value: _selectedPlatform,
                          decoration: const InputDecoration(
                            labelText: '选择平台',
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                          ),
                          items: _platforms.map((platform) {
                            return DropdownMenuItem(
                              value: platform,
                              child: Text(platform),
                            );
                          }).toList(),
                          onChanged: (value) {
                            setState(() {
                              _selectedPlatform = value!;
                            });
                            _filterProducts();
                          },
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          value: _selectedCategory,
                          decoration: const InputDecoration(
                            labelText: '选择分类',
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                          ),
                          items: _categories.map((category) {
                            return DropdownMenuItem(
                              value: category,
                              child: Text(category),
                            );
                          }).toList(),
                          onChanged: (value) {
                            setState(() {
                              _selectedCategory = value!;
                            });
                            _filterProducts();
                          },
                        ),
                      ),
                    ],
                  ),
                  
                  // 搜索区域
                  if (_showSearch) ...[
                    const SizedBox(height: 16),
                    _buildSearchFields(),
                  ],
                ],
              ),
            ),
            
            // 商品列表
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                itemCount: _filteredProducts.length,
                itemBuilder: (context, index) {
                  final product = _filteredProducts[index];
                  return _buildProductCard(product);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchFields() {
    return Column(
      children: [
        // 第一行：商品名称、平台编码
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _searchController,
                decoration: const InputDecoration(
                  labelText: '商品名称',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                onChanged: (value) => _filterProducts(),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: TextField(
                controller: _platformCodeController,
                decoration: const InputDecoration(
                  labelText: '平台编码',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                onChanged: (value) => _filterProducts(),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        
        // 第二行：商品编码、品牌
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _productCodeController,
                decoration: const InputDecoration(
                  labelText: '商品编码',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                onChanged: (value) => _filterProducts(),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: TextField(
                controller: _brandController,
                decoration: const InputDecoration(
                  labelText: '商品品牌',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                onChanged: (value) => _filterProducts(),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        
        // 第三行：型号、价格区间
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _modelController,
                decoration: const InputDecoration(
                  labelText: '型号',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                onChanged: (value) => _filterProducts(),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: _minPriceController,
                      decoration: const InputDecoration(
                        labelText: '最低价',
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      keyboardType: TextInputType.number,
                      onChanged: (value) => _filterProducts(),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: TextField(
                      controller: _maxPriceController,
                      decoration: const InputDecoration(
                        labelText: '最高价',
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      keyboardType: TextInputType.number,
                      onChanged: (value) => _filterProducts(),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        
        // 第四行：下拉选择
        Row(
          children: [
            Expanded(
              child: DropdownButtonFormField<String>(
                value: _selectedPriceStatus,
                decoration: const InputDecoration(
                  labelText: '报价状态',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                items: _priceStatus.map((status) {
                  return DropdownMenuItem(value: status, child: Text(status));
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedPriceStatus = value!;
                  });
                  _filterProducts();
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: DropdownButtonFormField<String>(
                value: _selectedBrandType,
                decoration: const InputDecoration(
                  labelText: '自主品牌',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                items: _brandTypes.map((type) {
                  return DropdownMenuItem(value: type, child: Text(type));
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedBrandType = value!;
                  });
                  _filterProducts();
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildProductCard(ProductItem product) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    product.name,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF2D3748),
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: product.isDisplayed ? Colors.green.withOpacity(0.1) : Colors.grey.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    product.isDisplayed ? '前台展示' : '不展示',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: product.isDisplayed ? Colors.green : Colors.grey,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: Text(
                    '平台: ${product.platform}',
                    style: const TextStyle(fontSize: 14, color: Color(0xFF718096)),
                  ),
                ),
                Expanded(
                  child: Text(
                    '品牌: ${product.brand}',
                    style: const TextStyle(fontSize: 14, color: Color(0xFF718096)),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                Expanded(
                  child: Text(
                    '平台编码: ${product.platformCode}',
                    style: const TextStyle(fontSize: 14, color: Color(0xFF718096)),
                  ),
                ),
                Expanded(
                  child: Text(
                    '商品编码: ${product.productCode}',
                    style: const TextStyle(fontSize: 14, color: Color(0xFF718096)),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                Expanded(
                  child: Text(
                    '型号: ${product.model}',
                    style: const TextStyle(fontSize: 14, color: Color(0xFF718096)),
                  ),
                ),
                Expanded(
                  child: Text(
                    '价格: ¥${product.price.toStringAsFixed(2)}',
                    style: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF6C63FF),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '更新时间: ${_formatDateTime(product.updateTime)}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
                if (product.isOwnBrand)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: Colors.blue.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Text(
                      '自主品牌',
                      style: TextStyle(
                        fontSize: 10,
                        color: Colors.blue,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _filterProducts() {
    setState(() {
      _filteredProducts = _products.where((product) {
        // 平台筛选
        if (_selectedPlatform != '全部平台' && product.platform != _selectedPlatform) {
          return false;
        }
        
        // 分类筛选
        if (_selectedCategory != '全部分类' && product.category != _selectedCategory) {
          return false;
        }
        
        // 商品名称搜索
        if (_searchController.text.isNotEmpty && 
            !product.name.toLowerCase().contains(_searchController.text.toLowerCase())) {
          return false;
        }
        
        // 平台编码搜索
        if (_platformCodeController.text.isNotEmpty && 
            !product.platformCode.toLowerCase().contains(_platformCodeController.text.toLowerCase())) {
          return false;
        }
        
        // 商品编码搜索
        if (_productCodeController.text.isNotEmpty && 
            !product.productCode.toLowerCase().contains(_productCodeController.text.toLowerCase())) {
          return false;
        }
        
        // 品牌搜索
        if (_brandController.text.isNotEmpty && 
            !product.brand.toLowerCase().contains(_brandController.text.toLowerCase())) {
          return false;
        }
        
        // 型号搜索
        if (_modelController.text.isNotEmpty && 
            !product.model.toLowerCase().contains(_modelController.text.toLowerCase())) {
          return false;
        }
        
        // 价格区间筛选
        if (_minPriceController.text.isNotEmpty) {
          final minPrice = double.tryParse(_minPriceController.text);
          if (minPrice != null && product.price < minPrice) {
            return false;
          }
        }
        
        if (_maxPriceController.text.isNotEmpty) {
          final maxPrice = double.tryParse(_maxPriceController.text);
          if (maxPrice != null && product.price > maxPrice) {
            return false;
          }
        }
        
        // 自主品牌筛选
        if (_selectedBrandType != '全部') {
          if (_selectedBrandType == '自主品牌' && !product.isOwnBrand) {
            return false;
          }
          if (_selectedBrandType == '代理品牌' && product.isOwnBrand) {
            return false;
          }
        }
        
        // 前台展示筛选
        if (_selectedDisplayStatus != '全部') {
          if (_selectedDisplayStatus == '前台展示' && !product.isDisplayed) {
            return false;
          }
          if (_selectedDisplayStatus == '不展示' && product.isDisplayed) {
            return false;
          }
        }
        
        return true;
      }).toList();
    });
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}

class ProductItem {
  final String name;
  final String platformCode;
  final String productCode;
  final String brand;
  final String model;
  final double price;
  final String platform;
  final String category;
  final bool isOwnBrand;
  final bool isDisplayed;
  final DateTime updateTime;

  ProductItem({
    required this.name,
    required this.platformCode,
    required this.productCode,
    required this.brand,
    required this.model,
    required this.price,
    required this.platform,
    required this.category,
    required this.isOwnBrand,
    required this.isDisplayed,
    required this.updateTime,
  });
}
