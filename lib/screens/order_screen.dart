import 'package:flutter/material.dart';

class OrderScreen extends StatefulWidget {
  const OrderScreen({super.key});

  @override
  State<OrderScreen> createState() => _OrderScreenState();
}

class _OrderScreenState extends State<OrderScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _showSearch = false;
  
  final List<String> _orderTabs = [
    '全部订单', '待接单', '待发货', '待收货', '待付款', '交易成功', '已废止', '已关闭', '异常订单'
  ];

  // 搜索控制器
  final TextEditingController _platformOrderController = TextEditingController();
  final TextEditingController _receiverNameController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _addressController = TextEditingController();
  final TextEditingController _productCodeController = TextEditingController();
  final TextEditingController _productNameController = TextEditingController();
  final TextEditingController _logisticsController = TextEditingController();

  String _selectedOrderSource = '全部';
  String _selectedOrderType = '全部';
  String _selectedDeliveryConfirm = '全部';
  String _selectedActualStatus = '全部';
  String _selectedFollower = '全部';
  String _selectedOrderStatus = '全部';
  String _selectedAuditStatus = '全部';
  String _selectedPaymentStatus = '全部';
  String _selectedSignStatus = '全部';
  String _selectedPurchaseStatus = '全部';

  final List<OrderItem> _orders = [
    OrderItem(
      id: 'ORD001',
      platformOrderId: 'JD2024001',
      source: '京东',
      type: '普通订单',
      status: '待发货',
      receiverName: '张三',
      phone: '13800138000',
      address: '北京市朝阳区xxx街道',
      productCode: 'IP15P001',
      productName: 'iPhone 15 Pro',
      logisticsNumber: '',
      follower: '李跟单',
      createTime: DateTime.now().subtract(const Duration(hours: 2)),
      amount: 8999.0,
    ),
    OrderItem(
      id: 'ORD002',
      platformOrderId: 'TM2024002',
      source: '天猫',
      type: '预售订单',
      status: '待收货',
      receiverName: '王五',
      phone: '13900139000',
      address: '上海市浦东新区xxx路',
      productCode: 'TP001',
      productName: '联想ThinkPad X1',
      logisticsNumber: 'SF2024001',
      follower: '赵跟单',
      createTime: DateTime.now().subtract(const Duration(days: 1)),
      amount: 12999.0,
    ),
  ];

  List<OrderItem> _filteredOrders = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _orderTabs.length, vsync: this);
    _filteredOrders = _orders;
  }

  @override
  void dispose() {
    _tabController.dispose();
    _platformOrderController.dispose();
    _receiverNameController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    _productCodeController.dispose();
    _productNameController.dispose();
    _logisticsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          '订单管理',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF6C63FF),
        elevation: 0,
        centerTitle: true,
        actions: [
          IconButton(
            icon: Icon(
              _showSearch ? Icons.search_off : Icons.search,
              color: Colors.white,
            ),
            onPressed: () {
              setState(() {
                _showSearch = !_showSearch;
              });
            },
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: _orderTabs.map((tab) => Tab(text: tab)).toList(),
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF6C63FF),
              Color(0xFFF7FAFC),
            ],
            stops: [0.0, 0.1],
          ),
        ),
        child: Column(
          children: [
            // 搜索区域
            if (_showSearch)
              Container(
                margin: const EdgeInsets.all(16),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 10,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: _buildSearchFields(),
              ),
            
            // 订单列表
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: _orderTabs.map((tab) {
                  return ListView.builder(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    itemCount: _filteredOrders.length,
                    itemBuilder: (context, index) {
                      final order = _filteredOrders[index];
                      return _buildOrderCard(order);
                    },
                  );
                }).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchFields() {
    return Column(
      children: [
        // 第一行：平台订单号、订单来源
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _platformOrderController,
                decoration: const InputDecoration(
                  labelText: '采购平台订单号',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                onChanged: (value) => _filterOrders(),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: DropdownButtonFormField<String>(
                value: _selectedOrderSource,
                decoration: const InputDecoration(
                  labelText: '订单来源',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                items: ['全部', '京东', '天猫', '淘宝', '拼多多'].map((source) {
                  return DropdownMenuItem(value: source, child: Text(source));
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedOrderSource = value!;
                  });
                  _filterOrders();
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        
        // 第二行：收件人姓名、联系电话
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _receiverNameController,
                decoration: const InputDecoration(
                  labelText: '收件人姓名',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                onChanged: (value) => _filterOrders(),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: TextField(
                controller: _phoneController,
                decoration: const InputDecoration(
                  labelText: '联系电话',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                onChanged: (value) => _filterOrders(),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        
        // 第三行：订单地址、商品编码
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _addressController,
                decoration: const InputDecoration(
                  labelText: '订单地址',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                onChanged: (value) => _filterOrders(),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: TextField(
                controller: _productCodeController,
                decoration: const InputDecoration(
                  labelText: '商品编码',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                onChanged: (value) => _filterOrders(),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        
        // 第四行：商品名称、物流单号
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _productNameController,
                decoration: const InputDecoration(
                  labelText: '商品名称',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                onChanged: (value) => _filterOrders(),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: TextField(
                controller: _logisticsController,
                decoration: const InputDecoration(
                  labelText: '物流单号',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                onChanged: (value) => _filterOrders(),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        
        // 第五行：跟单员、订单状态
        Row(
          children: [
            Expanded(
              child: DropdownButtonFormField<String>(
                value: _selectedFollower,
                decoration: const InputDecoration(
                  labelText: '跟单员',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                items: ['全部', '李跟单', '赵跟单', '王跟单'].map((follower) {
                  return DropdownMenuItem(value: follower, child: Text(follower));
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedFollower = value!;
                  });
                  _filterOrders();
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: DropdownButtonFormField<String>(
                value: _selectedOrderStatus,
                decoration: const InputDecoration(
                  labelText: '订单状态',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                items: ['全部', '待接单', '待发货', '待收货', '待付款', '交易成功'].map((status) {
                  return DropdownMenuItem(value: status, child: Text(status));
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedOrderStatus = value!;
                  });
                  _filterOrders();
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildOrderCard(OrderItem order) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 订单头部
            Row(
              children: [
                Expanded(
                  child: Text(
                    '订单号: ${order.platformOrderId}',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF2D3748),
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getStatusColor(order.status).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    order.status,
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: _getStatusColor(order.status),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            // 商品信息
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    order.productName,
                    style: const TextStyle(
                      fontSize: 15,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF2D3748),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '商品编码: ${order.productCode}',
                    style: const TextStyle(fontSize: 13, color: Color(0xFF718096)),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '金额: ¥${order.amount.toStringAsFixed(2)}',
                    style: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF6C63FF),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),
            
            // 收件人信息
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '收件人: ${order.receiverName}',
                        style: const TextStyle(fontSize: 14, color: Color(0xFF718096)),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        '电话: ${order.phone}',
                        style: const TextStyle(fontSize: 14, color: Color(0xFF718096)),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '来源: ${order.source}',
                        style: const TextStyle(fontSize: 14, color: Color(0xFF718096)),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        '跟单员: ${order.follower}',
                        style: const TextStyle(fontSize: 14, color: Color(0xFF718096)),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            
            // 地址信息
            Text(
              '地址: ${order.address}',
              style: const TextStyle(fontSize: 14, color: Color(0xFF718096)),
            ),
            
            if (order.logisticsNumber.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                '物流单号: ${order.logisticsNumber}',
                style: const TextStyle(fontSize: 14, color: Color(0xFF718096)),
              ),
            ],
            
            const SizedBox(height: 12),
            
            // 底部信息
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '下单时间: ${_formatDateTime(order.createTime)}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
                Row(
                  children: [
                    TextButton(
                      onPressed: () {
                        // 查看详情
                      },
                      child: const Text('查看详情'),
                    ),
                    TextButton(
                      onPressed: () {
                        // 处理订单
                      },
                      child: const Text('处理'),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case '待接单':
        return Colors.orange;
      case '待发货':
        return Colors.blue;
      case '待收货':
        return Colors.purple;
      case '待付款':
        return Colors.red;
      case '交易成功':
        return Colors.green;
      case '已废止':
      case '已关闭':
        return Colors.grey;
      case '异常订单':
        return Colors.red;
      default:
        return Colors.blue;
    }
  }

  void _filterOrders() {
    setState(() {
      _filteredOrders = _orders.where((order) {
        // 平台订单号搜索
        if (_platformOrderController.text.isNotEmpty && 
            !order.platformOrderId.toLowerCase().contains(_platformOrderController.text.toLowerCase())) {
          return false;
        }
        
        // 订单来源筛选
        if (_selectedOrderSource != '全部' && order.source != _selectedOrderSource) {
          return false;
        }
        
        // 收件人姓名搜索
        if (_receiverNameController.text.isNotEmpty && 
            !order.receiverName.toLowerCase().contains(_receiverNameController.text.toLowerCase())) {
          return false;
        }
        
        // 联系电话搜索
        if (_phoneController.text.isNotEmpty && 
            !order.phone.contains(_phoneController.text)) {
          return false;
        }
        
        // 订单地址搜索
        if (_addressController.text.isNotEmpty && 
            !order.address.toLowerCase().contains(_addressController.text.toLowerCase())) {
          return false;
        }
        
        // 商品编码搜索
        if (_productCodeController.text.isNotEmpty && 
            !order.productCode.toLowerCase().contains(_productCodeController.text.toLowerCase())) {
          return false;
        }
        
        // 商品名称搜索
        if (_productNameController.text.isNotEmpty && 
            !order.productName.toLowerCase().contains(_productNameController.text.toLowerCase())) {
          return false;
        }
        
        // 物流单号搜索
        if (_logisticsController.text.isNotEmpty && 
            !order.logisticsNumber.toLowerCase().contains(_logisticsController.text.toLowerCase())) {
          return false;
        }
        
        // 跟单员筛选
        if (_selectedFollower != '全部' && order.follower != _selectedFollower) {
          return false;
        }
        
        // 订单状态筛选
        if (_selectedOrderStatus != '全部' && order.status != _selectedOrderStatus) {
          return false;
        }
        
        return true;
      }).toList();
    });
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}

class OrderItem {
  final String id;
  final String platformOrderId;
  final String source;
  final String type;
  final String status;
  final String receiverName;
  final String phone;
  final String address;
  final String productCode;
  final String productName;
  final String logisticsNumber;
  final String follower;
  final DateTime createTime;
  final double amount;

  OrderItem({
    required this.id,
    required this.platformOrderId,
    required this.source,
    required this.type,
    required this.status,
    required this.receiverName,
    required this.phone,
    required this.address,
    required this.productCode,
    required this.productName,
    required this.logisticsNumber,
    required this.follower,
    required this.createTime,
    required this.amount,
  });
}
