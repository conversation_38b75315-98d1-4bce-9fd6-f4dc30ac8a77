import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';

class BiddingScreen extends StatefulWidget {
  const BiddingScreen({super.key});

  @override
  State<BiddingScreen> createState() => _BiddingScreenState();
}

class _BiddingScreenState extends State<BiddingScreen> {
  final List<BiddingItem> _biddingItems = [
    BiddingItem(
      id: '1',
      title: '电子产品采购竞价',
      status: '进行中',
      publishTime: DateTime.now().subtract(const Duration(hours: 2)),
      htmlContent: '<h1>电子产品采购竞价详情</h1><p>这是一个电子产品采购竞价的详细信息...</p>',
    ),
    BiddingItem(
      id: '2',
      title: '办公用品批量采购',
      status: '已结束',
      publishTime: DateTime.now().subtract(const Duration(days: 1)),
      htmlContent: '<h1>办公用品批量采购详情</h1><p>这是一个办公用品批量采购的详细信息...</p>',
    ),
    BiddingItem(
      id: '3',
      title: '设备维护服务竞价',
      status: '待开始',
      publishTime: DateTime.now().add(const Duration(hours: 6)),
      htmlContent: '<h1>设备维护服务竞价详情</h1><p>这是一个设备维护服务竞价的详细信息...</p>',
    ),
    BiddingItem(
      id: '4',
      title: '软件开发项目竞价',
      status: '进行中',
      publishTime: DateTime.now().subtract(const Duration(hours: 5)),
      htmlContent: '<h1>软件开发项目竞价详情</h1><p>这是一个软件开发项目竞价的详细信息...</p>',
    ),
    BiddingItem(
      id: '5',
      title: '物流运输服务竞价',
      status: '已结束',
      publishTime: DateTime.now().subtract(const Duration(days: 2)),
      htmlContent: '<h1>物流运输服务竞价详情</h1><p>这是一个物流运输服务竞价的详细信息...</p>',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          '竞价管理',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF6C63FF),
        elevation: 0,
        centerTitle: true,
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF6C63FF),
              Color(0xFFF7FAFC),
            ],
            stops: [0.0, 0.1],
          ),
        ),
        child: Column(
          children: [
            // 统计信息
            Container(
              margin: const EdgeInsets.all(16),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildStatItem('进行中', '2', Colors.green),
                  _buildStatItem('已结束', '2', Colors.grey),
                  _buildStatItem('待开始', '1', Colors.orange),
                ],
              ),
            ),
            
            // 竞价列表
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                itemCount: _biddingItems.length,
                itemBuilder: (context, index) {
                  final item = _biddingItems[index];
                  return _buildBiddingCard(item);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String count, Color color) {
    return Column(
      children: [
        Text(
          count,
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            color: Color(0xFF718096),
          ),
        ),
      ],
    );
  }

  Widget _buildBiddingCard(BiddingItem item) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () => _showBiddingDetail(item),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      item.title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF2D3748),
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: _getStatusColor(item.status).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      item.status,
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: _getStatusColor(item.status),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Icon(
                    Icons.access_time,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '发布时间: ${_formatDateTime(item.publishTime)}',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Text(
                    '点击查看详情',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[500],
                    ),
                  ),
                  const SizedBox(width: 4),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 12,
                    color: Colors.grey[500],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case '进行中':
        return Colors.green;
      case '已结束':
        return Colors.grey;
      case '待开始':
        return Colors.orange;
      default:
        return Colors.blue;
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  void _showBiddingDetail(BiddingItem item) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => BiddingDetailScreen(item: item),
      ),
    );
  }
}

class BiddingItem {
  final String id;
  final String title;
  final String status;
  final DateTime publishTime;
  final String htmlContent;

  BiddingItem({
    required this.id,
    required this.title,
    required this.status,
    required this.publishTime,
    required this.htmlContent,
  });
}

class BiddingDetailScreen extends StatefulWidget {
  final BiddingItem item;

  const BiddingDetailScreen({super.key, required this.item});

  @override
  State<BiddingDetailScreen> createState() => _BiddingDetailScreenState();
}

class _BiddingDetailScreenState extends State<BiddingDetailScreen> {
  late final WebViewController _controller;

  @override
  void initState() {
    super.initState();
    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..loadHtmlString(widget.item.htmlContent);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.item.title,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF6C63FF),
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: WebViewWidget(controller: _controller),
    );
  }
}
