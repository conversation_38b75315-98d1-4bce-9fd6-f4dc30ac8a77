import 'package:flutter/material.dart';

class FinanceScreen extends StatefulWidget {
  const FinanceScreen({super.key});

  @override
  State<FinanceScreen> createState() => _FinanceScreenState();
}

class _FinanceScreenState extends State<FinanceScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _showSearch = false;
  
  final List<String> _financeTabs = [
    '全部', '待申请', '待审核', '商务审核通过', '商务审核驳回', '财务审核驳回', '已开票', '已线下处理', '失效订单'
  ];

  // 搜索控制器
  final TextEditingController _orderNumberController = TextEditingController();
  final TextEditingController _invoiceNumberController = TextEditingController();
  final TextEditingController _invoiceTitleController = TextEditingController();

  String _selectedOrderStatus = '全部';
  String _selectedOrderSource = '全部';
  String _selectedInvoiceStatus = '全部';
  String _selectedFollower = '全部';
  DateTime? _startDate;
  DateTime? _endDate;
  double? _minAmount;
  double? _maxAmount;

  final List<FinanceItem> _financeItems = [
    FinanceItem(
      id: 'FIN001',
      orderNumber: 'ORD001',
      orderStatus: '交易成功',
      orderSource: '京东',
      invoiceStatus: '待申请',
      follower: '李跟单',
      orderTime: DateTime.now().subtract(const Duration(days: 2)),
      orderAmount: 8999.0,
      invoiceNumber: '',
      invoiceTitle: '北京科技有限公司',
    ),
    FinanceItem(
      id: 'FIN002',
      orderNumber: 'ORD002',
      orderStatus: '交易成功',
      orderSource: '天猫',
      invoiceStatus: '已开票',
      follower: '赵跟单',
      orderTime: DateTime.now().subtract(const Duration(days: 5)),
      orderAmount: 12999.0,
      invoiceNumber: 'INV2024001',
      invoiceTitle: '上海贸易有限公司',
    ),
    FinanceItem(
      id: 'FIN003',
      orderNumber: 'ORD003',
      orderStatus: '交易成功',
      orderSource: '拼多多',
      invoiceStatus: '商务审核通过',
      follower: '王跟单',
      orderTime: DateTime.now().subtract(const Duration(days: 3)),
      orderAmount: 5999.0,
      invoiceNumber: '',
      invoiceTitle: '深圳电子有限公司',
    ),
  ];

  List<FinanceItem> _filteredItems = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _financeTabs.length, vsync: this);
    _filteredItems = _financeItems;
  }

  @override
  void dispose() {
    _tabController.dispose();
    _orderNumberController.dispose();
    _invoiceNumberController.dispose();
    _invoiceTitleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          '财务管理',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF6C63FF),
        elevation: 0,
        centerTitle: true,
        actions: [
          IconButton(
            icon: Icon(
              _showSearch ? Icons.search_off : Icons.search,
              color: Colors.white,
            ),
            onPressed: () {
              setState(() {
                _showSearch = !_showSearch;
              });
            },
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: _financeTabs.map((tab) => Tab(text: tab)).toList(),
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF6C63FF),
              Color(0xFFF7FAFC),
            ],
            stops: [0.0, 0.1],
          ),
        ),
        child: Column(
          children: [
            // 统计信息
            Container(
              margin: const EdgeInsets.all(16),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildStatItem('待处理', '2', Colors.orange),
                  _buildStatItem('已开票', '1', Colors.green),
                  _buildStatItem('总金额', '¥27,997', Colors.blue),
                ],
              ),
            ),
            
            // 搜索区域
            if (_showSearch)
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 16),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 10,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: _buildSearchFields(),
              ),
            
            if (_showSearch) const SizedBox(height: 16),
            
            // 财务列表
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: _financeTabs.map((tab) {
                  return ListView.builder(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    itemCount: _filteredItems.length,
                    itemBuilder: (context, index) {
                      final item = _filteredItems[index];
                      return _buildFinanceCard(item);
                    },
                  );
                }).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            color: Color(0xFF718096),
          ),
        ),
      ],
    );
  }

  Widget _buildSearchFields() {
    return Column(
      children: [
        // 第一行：订单号、发票号
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _orderNumberController,
                decoration: const InputDecoration(
                  labelText: '订单号',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                onChanged: (value) => _filterItems(),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: TextField(
                controller: _invoiceNumberController,
                decoration: const InputDecoration(
                  labelText: '发票号',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                onChanged: (value) => _filterItems(),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        
        // 第二行：发票抬头、订单状态
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _invoiceTitleController,
                decoration: const InputDecoration(
                  labelText: '发票抬头',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                onChanged: (value) => _filterItems(),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: DropdownButtonFormField<String>(
                value: _selectedOrderStatus,
                decoration: const InputDecoration(
                  labelText: '订单状态',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                items: ['全部', '交易成功', '待付款', '已关闭'].map((status) {
                  return DropdownMenuItem(value: status, child: Text(status));
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedOrderStatus = value!;
                  });
                  _filterItems();
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        
        // 第三行：订单来源、发票状态
        Row(
          children: [
            Expanded(
              child: DropdownButtonFormField<String>(
                value: _selectedOrderSource,
                decoration: const InputDecoration(
                  labelText: '订单来源',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                items: ['全部', '京东', '天猫', '淘宝', '拼多多'].map((source) {
                  return DropdownMenuItem(value: source, child: Text(source));
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedOrderSource = value!;
                  });
                  _filterItems();
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: DropdownButtonFormField<String>(
                value: _selectedInvoiceStatus,
                decoration: const InputDecoration(
                  labelText: '发票状态',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                items: ['全部', '待申请', '待审核', '已开票', '已驳回'].map((status) {
                  return DropdownMenuItem(value: status, child: Text(status));
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedInvoiceStatus = value!;
                  });
                  _filterItems();
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        
        // 第四行：跟单员、金额区间
        Row(
          children: [
            Expanded(
              child: DropdownButtonFormField<String>(
                value: _selectedFollower,
                decoration: const InputDecoration(
                  labelText: '跟单员',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                items: ['全部', '李跟单', '赵跟单', '王跟单'].map((follower) {
                  return DropdownMenuItem(value: follower, child: Text(follower));
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedFollower = value!;
                  });
                  _filterItems();
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Row(
                children: [
                  Expanded(
                    child: TextField(
                      decoration: const InputDecoration(
                        labelText: '最低金额',
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      keyboardType: TextInputType.number,
                      onChanged: (value) {
                        _minAmount = double.tryParse(value);
                        _filterItems();
                      },
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: TextField(
                      decoration: const InputDecoration(
                        labelText: '最高金额',
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      keyboardType: TextInputType.number,
                      onChanged: (value) {
                        _maxAmount = double.tryParse(value);
                        _filterItems();
                      },
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildFinanceCard(FinanceItem item) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 头部信息
            Row(
              children: [
                Expanded(
                  child: Text(
                    '订单号: ${item.orderNumber}',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF2D3748),
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getInvoiceStatusColor(item.invoiceStatus).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    item.invoiceStatus,
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: _getInvoiceStatusColor(item.invoiceStatus),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            // 金额信息
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          '订单总金额',
                          style: TextStyle(
                            fontSize: 12,
                            color: Color(0xFF718096),
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '¥${item.orderAmount.toStringAsFixed(2)}',
                          style: const TextStyle(
                            fontSize: 18,
                            color: Color(0xFF6C63FF),
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (item.invoiceNumber.isNotEmpty)
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            '发票号',
                            style: TextStyle(
                              fontSize: 12,
                              color: Color(0xFF718096),
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            item.invoiceNumber,
                            style: const TextStyle(
                              fontSize: 14,
                              color: Color(0xFF2D3748),
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
            const SizedBox(height: 12),
            
            // 详细信息
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '订单来源: ${item.orderSource}',
                        style: const TextStyle(fontSize: 14, color: Color(0xFF718096)),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '订单状态: ${item.orderStatus}',
                        style: const TextStyle(fontSize: 14, color: Color(0xFF718096)),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '跟单员: ${item.follower}',
                        style: const TextStyle(fontSize: 14, color: Color(0xFF718096)),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '下单时间: ${_formatDateTime(item.orderTime)}',
                        style: const TextStyle(fontSize: 14, color: Color(0xFF718096)),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            
            // 发票抬头
            Text(
              '发票抬头: ${item.invoiceTitle}',
              style: const TextStyle(fontSize: 14, color: Color(0xFF718096)),
            ),
            const SizedBox(height: 12),
            
            // 操作按钮
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                if (item.invoiceStatus == '待申请')
                  ElevatedButton(
                    onPressed: () {
                      // 申请开票
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF6C63FF),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    ),
                    child: const Text('申请开票'),
                  ),
                if (item.invoiceStatus == '待审核')
                  Row(
                    children: [
                      OutlinedButton(
                        onPressed: () {
                          // 驳回
                        },
                        child: const Text('驳回'),
                      ),
                      const SizedBox(width: 8),
                      ElevatedButton(
                        onPressed: () {
                          // 审核通过
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                        ),
                        child: const Text('审核通过'),
                      ),
                    ],
                  ),
                if (item.invoiceStatus == '已开票')
                  ElevatedButton(
                    onPressed: () {
                      // 查看发票
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    ),
                    child: const Text('查看发票'),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Color _getInvoiceStatusColor(String status) {
    switch (status) {
      case '待申请':
        return Colors.orange;
      case '待审核':
        return Colors.blue;
      case '商务审核通过':
        return Colors.green;
      case '商务审核驳回':
      case '财务审核驳回':
        return Colors.red;
      case '已开票':
        return Colors.green;
      case '已线下处理':
        return Colors.purple;
      case '失效订单':
        return Colors.grey;
      default:
        return Colors.blue;
    }
  }

  void _filterItems() {
    setState(() {
      _filteredItems = _financeItems.where((item) {
        // 订单号搜索
        if (_orderNumberController.text.isNotEmpty && 
            !item.orderNumber.toLowerCase().contains(_orderNumberController.text.toLowerCase())) {
          return false;
        }
        
        // 发票号搜索
        if (_invoiceNumberController.text.isNotEmpty && 
            !item.invoiceNumber.toLowerCase().contains(_invoiceNumberController.text.toLowerCase())) {
          return false;
        }
        
        // 发票抬头搜索
        if (_invoiceTitleController.text.isNotEmpty && 
            !item.invoiceTitle.toLowerCase().contains(_invoiceTitleController.text.toLowerCase())) {
          return false;
        }
        
        // 订单状态筛选
        if (_selectedOrderStatus != '全部' && item.orderStatus != _selectedOrderStatus) {
          return false;
        }
        
        // 订单来源筛选
        if (_selectedOrderSource != '全部' && item.orderSource != _selectedOrderSource) {
          return false;
        }
        
        // 发票状态筛选
        if (_selectedInvoiceStatus != '全部' && item.invoiceStatus != _selectedInvoiceStatus) {
          return false;
        }
        
        // 跟单员筛选
        if (_selectedFollower != '全部' && item.follower != _selectedFollower) {
          return false;
        }
        
        // 金额区间筛选
        if (_minAmount != null && item.orderAmount < _minAmount!) {
          return false;
        }
        
        if (_maxAmount != null && item.orderAmount > _maxAmount!) {
          return false;
        }
        
        return true;
      }).toList();
    });
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}

class FinanceItem {
  final String id;
  final String orderNumber;
  final String orderStatus;
  final String orderSource;
  final String invoiceStatus;
  final String follower;
  final DateTime orderTime;
  final double orderAmount;
  final String invoiceNumber;
  final String invoiceTitle;

  FinanceItem({
    required this.id,
    required this.orderNumber,
    required this.orderStatus,
    required this.orderSource,
    required this.invoiceStatus,
    required this.follower,
    required this.orderTime,
    required this.orderAmount,
    required this.invoiceNumber,
    required this.invoiceTitle,
  });
}
