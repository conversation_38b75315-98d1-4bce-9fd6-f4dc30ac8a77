PODS:
  - Flutter (1.0.0)
  - fluwx (0.0.1):
    - Flutter
    - fluwx/pay (= 0.0.1)
  - fluwx/pay (0.0.1):
    - Flutter
    - WechatOpenSDK-XCFramework (~> 2.0.4)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - WechatOpenSDK-XCFramework (2.0.4)

DEPENDENCIES:
  - Flutter (from `Flutter`)
  - fluwx (from `.symlinks/plugins/fluwx/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)

SPEC REPOS:
  trunk:
    - WechatOpenSDK-XCFramework

EXTERNAL SOURCES:
  Flutter:
    :path: Flutter
  fluwx:
    :path: ".symlinks/plugins/fluwx/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"

SPEC CHECKSUMS:
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  fluwx: 6bf9c5a3a99ad31b0de137dd92370a0d10a60f4b
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  WechatOpenSDK-XCFramework: 36fb2bea0754266c17184adf4963d7e6ff98b69f

PODFILE CHECKSUM: 4305caec6b40dde0ae97be1573c53de1882a07e5

COCOAPODS: 1.16.2
